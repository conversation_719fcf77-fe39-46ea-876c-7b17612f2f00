<template>
  <div class="guide-point-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解点' : '新增讲解点' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="产品ID" prop="productId">
          <el-select
            v-model="form.productId"
            placeholder="请选择产品"
            style="width: 100%"
            filterable
            clearable
            :loading="productLoading"
          >
            <el-option
              v-for="product in productList"
              :key="product.id"
              :label="`${product.name} (ID: ${product.id})`"
              :value="product.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="讲解点标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入讲解点标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲解点位置" prop="location">
          <el-input
            v-model="form.location"
            placeholder="请输入讲解点位置"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲解时长" prop="duration">
          <el-input
            v-model="form.duration"
            placeholder="请输入讲解时长（如：5分钟、10分钟等）"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="示例图片" prop="exampleImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.exampleImageUrl" :src="form.exampleImageUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="音频URL" prop="audioUrl">
          <div class="upload-container">
            <el-input
              v-model="form.audioUrl"
              placeholder="音频文件URL"
              readonly
              style="margin-bottom: 10px"
            />
            <el-upload
              class="audio-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="beforeAudioUpload"
              :http-request="handleAudioUpload"
              :disabled="audioUploadLoading"
            >
              <el-button type="primary" :loading="audioUploadLoading">
                {{ audioUploadLoading ? '上传中...' : '上传音频' }}
              </el-button>
            </el-upload>
            <div class="upload-tip">支持mp3、wav、m4a格式，文件大小不超过10MB</div>
          </div>
        </el-form-item>

        <el-form-item label="分类标签" prop="categoryTags">
          <el-input
            v-model="form.categoryTags"
            placeholder="请输入分类标签，多个标签用逗号分隔"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲解点描述图" prop="descriptionImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeDescriptionImageUpload"
            :http-request="handleDescriptionImageUpload"
            :disabled="descriptionImageUploadLoading"
          >
            <img v-if="form.descriptionImageUrl" :src="form.descriptionImageUrl" class="image" />
            <div v-else-if="descriptionImageUploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="讲解点描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入讲解点描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { getGuidePointById, createGuidePoint, updateGuidePoint } from '@/api/guidePoint'
import { uploadImage, uploadFile } from '@/api/upload'
import { getProductList } from '@/api/product'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const audioUploadLoading = ref(false)
const descriptionImageUploadLoading = ref(false)
const productLoading = ref(false)
const formRef = ref()
const productList = ref([])

const form = reactive({
  productId: null,
  title: '',
  location: '',
  duration: '',
  exampleImageUrl: '',
  audioUrl: '',
  categoryTags: '',
  description: '',
  descriptionImageUrl: '',
  status: 1
})

// 表单验证规则
const rules = {
  productId: [
    { required: true, message: '请选择产品', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入讲解点标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 获取产品列表
const fetchProductList = async () => {
  try {
    productLoading.value = true
    const { data } = await getProductList()
    productList.value = data || []
  } catch (error) {
    ElMessage.error('获取产品列表失败')
    console.error(error)
  } finally {
    productLoading.value = false
  }
}

// 音频上传前验证
const beforeAudioUpload = (file) => {
  const isAudio = file.type.startsWith('audio/') ||
                  file.type === 'audio/mp3' ||
                  file.type === 'audio/wav' ||
                  file.type === 'audio/m4a'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('音频文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 音频上传
const handleAudioUpload = async (options) => {
  const { file } = options

  try {
    audioUploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '音频上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadFile(file, (progress) => {
      loadingInstance.setText(`音频上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      form.audioUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('音频上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('音频上传失败:', error)
    ElMessage.error(error.message || '音频上传失败，请重试')
  } finally {
    audioUploadLoading.value = false
  }
}

// 图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      form.exampleImageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 描述图片上传前验证
const beforeDescriptionImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/webp'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG/GIF/WEBP 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 描述图片上传
const handleDescriptionImageUpload = async (options) => {
  const { file } = options

  try {
    descriptionImageUploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      form.descriptionImageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    descriptionImageUploadLoading.value = false
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getGuidePointById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取讲解点详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateGuidePoint(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createGuidePoint(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/guide-point/list')
}

// 初始化
onMounted(() => {
  fetchProductList()
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.guide-point-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 300px;
  height: 100px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-uploader-loading {
  width: 300px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.upload-container {
  width: 100%;
}

.audio-uploader {
  margin-bottom: 5px;
}
</style>
