package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 讲解点实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("point_table")
@ApiModel(value = "GuidePoint对象", description = "讲解点信息")
public class GuidePoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "讲解点ID")
    @TableId(value = "point_id", type = IdType.AUTO)
    private Integer pointId;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    @NotNull(message = "产品ID不能为空")
    private Integer productId;

    @ApiModelProperty(value = "示例图片URL")
    @TableField("example_image_url")
    private String exampleImageUrl;

    @ApiModelProperty(value = "讲解点标题")
    @TableField("title")
    @NotBlank(message = "讲解点标题不能为空")
    private String title;

    @ApiModelProperty(value = "讲解时长")
    @TableField("duration")
    private LocalTime duration;

    @ApiModelProperty(value = "讲解点位置")
    @TableField("location")
    private String location;

    @ApiModelProperty(value = "音频URL")
    @TableField("audio_url")
    private String audioUrl;

    @ApiModelProperty(value = "分类标签")
    @TableField("category_tags")
    private String categoryTags;

    @ApiModelProperty(value = "讲解点描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
