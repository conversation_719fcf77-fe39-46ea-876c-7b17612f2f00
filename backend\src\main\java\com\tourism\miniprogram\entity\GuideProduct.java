package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 讲解产品实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product_table")
@ApiModel(value = "GuideProduct对象", description = "讲解产品信息")
public class GuideProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品ID")
    @TableId(value = "product_id", type = IdType.AUTO)
    private Integer productId;

    @ApiModelProperty(value = "产品标题")
    @TableField("title")
    @NotBlank(message = "产品标题不能为空")
    private String title;

    @ApiModelProperty(value = "景区ID")
    @TableField("scenic_id")
    @NotNull(message = "景区ID不能为空")
    private Integer scenicId;

    @ApiModelProperty(value = "讲解点数量")
    @TableField("point_count")
    private Integer pointCount;

    @ApiModelProperty(value = "讲解时长")
    @TableField("duration")
    private String duration;

    @ApiModelProperty(value = "产品价格")
    @TableField("price")
    @NotNull(message = "产品价格不能为空")
    private BigDecimal price;

    @ApiModelProperty(value = "背景图片URL")
    @TableField("background_image_url")
    private String backgroundImageUrl;

    @ApiModelProperty(value = "示例视频URL")
    @TableField("example_video_url")
    private String exampleVideoUrl;

    @ApiModelProperty(value = "讲解员ID")
    @TableField("lecturer_id")
    private Integer lecturerId;

    @ApiModelProperty(value = "地图URL")
    @TableField("map_url")
    private String mapUrl;

    @ApiModelProperty(value = "开始收听图片URL")
    @TableField("start_listening_image_url")
    private String startListeningImageUrl;

    @ApiModelProperty(value = "产品描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
